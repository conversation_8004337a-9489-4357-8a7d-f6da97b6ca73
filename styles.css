/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #0a0a0a;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #ffffff;
    padding: 15px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 10px;
    }
}

.logo-img {
    height: 50px;
    width: auto;
}

.nav-buttons {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 25px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 14px;
    border: 2px solid transparent;
}

.btn-login {
    background: #ff6b35;
    color: #ffffff;
    border-color: #ff6b35;
    font-size: 16px;
    font-weight: 600;
}

.btn-login:hover {
    background: #e55a2b;
    color: #ffffff;
}

.btn-register {
    background: #dc3545;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
}

.btn-register:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 28px;
    height: 3px;
    background: #333;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover span {
    background: #ff6b35;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background: #ff6b35;
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background: #ff6b35;
}

.mobile-nav {
    display: none;
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #ff8c00, #ffa500, #ffb347);
    padding: 25px 20px;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    border-bottom: 3px solid #ff6b35;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-content {
    text-align: center;
}

.mobile-logo img {
    height: 40px;
    margin-bottom: 20px;
}

.mobile-nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    margin-top: 10px;
}

.mobile-nav-buttons .btn {
    width: 200px;
    padding: 15px 25px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.mobile-nav-buttons .btn-login {
    background: #fff;
    color: #ff6b35;
    border: 2px solid #ff6b35;
}

.mobile-nav-buttons .btn-login:hover {
    background: #ff6b35;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.mobile-nav-buttons .btn-register {
    background: #dc3545;
    color: #fff;
    border: 2px solid #dc3545;
}

.mobile-nav-buttons .btn-register:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Main Content */
.main {
    margin-top: 80px;
    background: #0a0a0a;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    padding: 80px 0;
    text-align: center;
    color: #fff;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 40px;
    line-height: 1.2;
}

.brand-ph {
    color: #ff6b35;
}

.brand-lago {
    color: #f7931e;
}

.welcome-section {
    max-width: 900px;
    margin: 0 auto;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #fff;
}

.welcome-text p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* Promotional Banner Section */
.promo-banner {
    padding: 15px 0;
    background: #111;
    position: relative;
    overflow: hidden;
}

.banner-slider {
    position: relative;
    width: 100%;
    height: 420px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    background: #1a1a1a;
    margin: 0 auto;
    max-width: 1100px;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
    padding: 0;
    box-sizing: border-box;
}

.banner-slide.active {
    opacity: 1;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
    transition: transform 0.3s ease;
    display: block;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.banner-slide img:hover {
    transform: scale(1.02);
}

.banner-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #ff8c00;
    transform: scale(1.2);
}

.banner-arrows {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.arrow-left, .arrow-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.8rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.arrow-left {
    left: 10px;
}

.arrow-right {
    right: 10px;
}

.arrow-left:hover, .arrow-right:hover {
    background: rgba(255, 140, 0, 0.8);
    border-color: #fff;
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

/* Invite Section */
.invite-section {
    padding: 40px 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    text-align: center;
}

.invite-btn {
    display: inline-block;
    background: #fff;
    color: #ff6b35;
    padding: 20px 40px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.invite-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Floating Play Now Button */
.floating-play-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: #fff;
    padding: 15px 25px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0;
    transform: translateY(100px);
    pointer-events: none;
}

.floating-play-btn.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.floating-play-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.6);
}

.floating-play-btn .play-icon {
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Content Sections */
.about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion {
    padding: 60px 0;
    background: #111;
    color: #fff;
}

.about:nth-child(odd), .slots:nth-child(odd), .payouts:nth-child(odd), .support:nth-child(odd), .signup:nth-child(odd), .bonuses:nth-child(odd), .security:nth-child(odd), .responsible:nth-child(odd), .tips:nth-child(odd), .faq:nth-child(odd), .conclusion:nth-child(odd) {
    background: #0a0a0a;
}

.about h2, .slots h2, .payouts h2, .support h2, .signup h2, .bonuses h2, .security h2, .responsible h2, .tips h2, .faq h2, .conclusion h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: #fff;
}

.about p, .slots p, .payouts p, .support p, .security p, .responsible p, .conclusion p {
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    opacity: 0.9;
}

/* Steps and Lists */
.steps, .bonus-list, .tips-list, .faq-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.step, .bonus-item, .tip-item, .faq-item {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.step:hover, .bonus-item:hover, .tip-item:hover, .faq-item:hover {
    transform: translateY(-5px);
}

.step h3, .bonus-item h3, .tip-item h3, .faq-item h3 {
    color: #ff6b35;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.step p, .bonus-item p, .tip-item p, .faq-item p {
    font-size: 1rem;
    line-height: 1.6;
    text-align: left;
}

/* Telegram and Recharge Images */
.telegram, .recharge, .rebates {
    padding: 40px 0;
    background: #111;
    text-align: center;
}

.telegram-images, .recharge-images, .rebates-images {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.telegram-images img, .recharge-images img, .rebates-images img, .rebates-img {
    max-width: 400px;
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

.telegram-images img:hover, .recharge-images img:hover, .rebates-images img:hover, .rebates-img:hover {
    transform: scale(1.05);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #fff;
    padding: 60px 0 30px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    text-align: center;
}

.footer-logo img {
    height: 60px;
    margin-bottom: 40px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.footer-section h3 {
    color: #ff6b35;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #f7931e;
}

.provider-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.provider-logos img {
    height: 50px;
    width: auto;
    transition: transform 0.3s ease;
}

.provider-logos img:hover {
    transform: scale(1.1);
}

.footer-copyright {
    border-top: 1px solid #333;
    padding-top: 30px;
    text-align: center;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-buttons {
        display: flex;
        gap: 10px;
    }

    .nav-buttons .btn {
        padding: 8px 15px;
        font-size: 0.9rem;
        border-radius: 20px;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .mobile-nav {
        display: none !important;
    }
    
    .hero-title {
        font-size: 1.8rem;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .gallery-track img {
        height: 150px;
        width: 200px;
    }
    
    .steps, .bonus-list, .tips-list, .faq-list {
        grid-template-columns: 1fr;
    }
    
    .telegram-images, .recharge-images {
        flex-direction: column;
        align-items: center;
    }
    
    .provider-logos {
        gap: 15px;
    }
    
    .provider-logos img {
        height: 40px;
    }

    /* Banner responsive styles */
    .promo-banner {
        padding: 12px 0;
    }

    .banner-slider {
        height: 340px;
        border-radius: 12px;
        max-width: 98%;
    }

    .banner-slide {
        padding: 0;
    }

    .banner-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 10px;
    }

    .arrow-left, .arrow-right {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
        border-width: 1px;
    }

    .arrow-left {
        left: 5px;
    }

    .arrow-right {
        right: 5px;
    }

    .banner-dots {
        margin-top: 12px;
        gap: 8px;
    }

    .dot {
        width: 8px;
        height: 8px;
    }

    /* Floating button responsive */
    .floating-play-btn {
        bottom: 15px;
        right: 15px;
        padding: 12px 20px;
        font-size: 1rem;
    }

    .floating-play-btn .play-icon {
        font-size: 1.1rem;
    }
}

/* Extra small mobile devices (phones in portrait) */
@media (max-width: 480px) {
    .nav-buttons {
        gap: 8px;
    }

    .nav-buttons .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 60px;
    }

    .logo-img {
        height: 40px;
    }

    .promo-banner {
        padding: 10px 0;
    }

    .banner-slider {
        height: 260px;
        border-radius: 10px;
        max-width: 98%;
    }

    .banner-slide {
        padding: 0;
    }

    .banner-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 8px;
    }

    .arrow-left, .arrow-right {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        border-width: 1px;
    }

    .arrow-left {
        left: 3px;
    }

    .arrow-right {
        right: 3px;
    }

    .banner-dots {
        margin-top: 10px;
        gap: 6px;
    }

    .dot {
        width: 6px;
        height: 6px;
    }

    /* Floating button responsive */
    .floating-play-btn {
        bottom: 10px;
        right: 10px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .floating-play-btn .play-icon {
        font-size: 1rem;
    }
}

/* Medium tablets */
@media (min-width: 769px) and (max-width: 1024px) {
    .banner-slider {
        height: 350px;
    }

    .banner-slide {
        padding: 0;
    }

    .arrow-left, .arrow-right {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .arrow-left {
        left: 15px;
    }

    .arrow-right {
        right: 15px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }
}
